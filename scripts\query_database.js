const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
    host: 'localhost',
    user: 'root',
    password: '123456',
    database: 'fangfang_game',
    charset: 'utf8mb4',
    port: 3306
};

/**
 * 查询所有排行榜数据
 */
async function queryAllLeaderboard() {
    console.log('📊 查询所有排行榜数据...\n');
    
    try {
        const connection = await mysql.createConnection(dbConfig);
        
        const [rows] = await connection.execute(`
            SELECT 
                id,
                player_name,
                score,
                time_used,
                time_limit,
                game_type,
                CONCAT(FLOOR(time_used / 60), ':', LPAD(time_used % 60, 2, '0')) as time_formatted,
                DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:%s') as created_time
            FROM leaderboard 
            ORDER BY created_at DESC
        `);
        
        console.log(`找到 ${rows.length} 条记录：`);
        console.log('ID\t玩家名\t\t分数\t用时\t\t游戏类型\t创建时间');
        console.log('─'.repeat(80));
        
        rows.forEach(row => {
            const playerName = row.player_name.padEnd(8, ' ');
            const gameType = (row.game_type || 'match3').padEnd(8, ' ');
            console.log(`${row.id}\t${playerName}\t${row.score}\t${row.time_formatted}\t\t${gameType}\t${row.created_time}`);
        });
        
        await connection.end();
        
    } catch (error) {
        console.error('❌ 查询失败:', error.message);
    }
}

/**
 * 查询排行榜前N名
 */
async function queryTopPlayers(limit = 10) {
    console.log(`🏆 查询排行榜前${limit}名...\n`);
    
    try {
        const connection = await mysql.createConnection(dbConfig);
        
        const [rows] = await connection.execute(`
            SELECT
                ROW_NUMBER() OVER (ORDER BY score DESC, time_used ASC) as rank_num,
                player_name,
                score,
                time_used,
                CONCAT(FLOOR(time_used / 60), ':', LPAD(time_used % 60, 2, '0')) as time_formatted,
                COALESCE(game_type, 'match3') as game_type,
                DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:%s') as created_time
            FROM leaderboard
            ORDER BY score DESC, time_used ASC
            LIMIT ${limit}
        `);
        
        console.log('排名\t玩家名\t\t分数\t用时\t\t游戏类型\t创建时间');
        console.log('─'.repeat(80));
        
        rows.forEach(row => {
            const playerName = row.player_name.padEnd(8, ' ');
            const gameType = (row.game_type || 'match3').padEnd(8, ' ');
            console.log(`${row.rank_num}\t${playerName}\t${row.score}\t${row.time_formatted}\t\t${gameType}\t${row.created_time}`);
        });
        
        await connection.end();
        
    } catch (error) {
        console.error('❌ 查询失败:', error.message);
    }
}

/**
 * 按玩家名查询记录
 */
async function queryByPlayer(playerName) {
    console.log(`👤 查询玩家 "${playerName}" 的记录...\n`);
    
    try {
        const connection = await mysql.createConnection(dbConfig);
        
        const [rows] = await connection.execute(`
            SELECT 
                id,
                player_name,
                score,
                time_used,
                CONCAT(FLOOR(time_used / 60), ':', LPAD(time_used % 60, 2, '0')) as time_formatted,
                game_type,
                DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:%s') as created_time
            FROM leaderboard 
            WHERE player_name LIKE ?
            ORDER BY score DESC, created_at DESC
        `, [`%${playerName}%`]);
        
        if (rows.length === 0) {
            console.log('❌ 未找到相关记录');
        } else {
            console.log(`找到 ${rows.length} 条记录：`);
            console.log('ID\t分数\t用时\t\t游戏类型\t创建时间');
            console.log('─'.repeat(60));
            
            rows.forEach(row => {
                const gameType = (row.game_type || 'match3').padEnd(8, ' ');
                console.log(`${row.id}\t${row.score}\t${row.time_formatted}\t\t${gameType}\t${row.created_time}`);
            });
        }
        
        await connection.end();
        
    } catch (error) {
        console.error('❌ 查询失败:', error.message);
    }
}

/**
 * 查询数据库统计信息
 */
async function queryStats() {
    console.log('📈 查询数据库统计信息...\n');
    
    try {
        const connection = await mysql.createConnection(dbConfig);
        
        // 总记录数
        const [countRows] = await connection.execute('SELECT COUNT(*) as total FROM leaderboard');
        console.log(`📊 总记录数: ${countRows[0].total}`);
        
        // 按游戏类型统计
        const [gameTypeRows] = await connection.execute(`
            SELECT 
                COALESCE(game_type, 'match3') as game_type,
                COUNT(*) as count,
                MAX(score) as max_score,
                AVG(score) as avg_score
            FROM leaderboard 
            GROUP BY COALESCE(game_type, 'match3')
        `);
        
        console.log('\n🎮 按游戏类型统计:');
        gameTypeRows.forEach(row => {
            console.log(`  ${row.game_type}: ${row.count}条记录, 最高分: ${row.max_score}, 平均分: ${Math.round(row.avg_score)}`);
        });
        
        // 最近7天的记录
        const [recentRows] = await connection.execute(`
            SELECT COUNT(*) as count 
            FROM leaderboard 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        `);
        console.log(`\n📅 最近7天新增记录: ${recentRows[0].count}条`);
        
        // 最高分记录
        const [topScoreRows] = await connection.execute(`
            SELECT 
                player_name,
                score,
                CONCAT(FLOOR(time_used / 60), ':', LPAD(time_used % 60, 2, '0')) as time_formatted,
                DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:%s') as created_time
            FROM leaderboard 
            ORDER BY score DESC 
            LIMIT 1
        `);
        
        if (topScoreRows.length > 0) {
            const top = topScoreRows[0];
            console.log(`\n🏆 历史最高分: ${top.player_name} - ${top.score}分 (${top.time_formatted}) - ${top.created_time}`);
        }
        
        await connection.end();
        
    } catch (error) {
        console.error('❌ 查询失败:', error.message);
    }
}

/**
 * 主函数 - 处理命令行参数
 */
async function main() {
    const args = process.argv.slice(2);
    const command = args[0];
    
    console.log('🎮 芳芳消消乐 - 数据库查询工具');
    console.log('================================\n');
    
    switch (command) {
        case 'all':
            await queryAllLeaderboard();
            break;
            
        case 'top':
            const limit = parseInt(args[1]) || 10;
            await queryTopPlayers(limit);
            break;
            
        case 'player':
            const playerName = args[1];
            if (!playerName) {
                console.log('❌ 请提供玩家名称');
                console.log('用法: node query_database.js player 玩家名');
                return;
            }
            await queryByPlayer(playerName);
            break;
            
        case 'stats':
            await queryStats();
            break;
            
        default:
            console.log('📖 使用说明:');
            console.log('  node query_database.js all           - 查询所有记录');
            console.log('  node query_database.js top [数量]     - 查询排行榜前N名 (默认10名)');
            console.log('  node query_database.js player 玩家名  - 查询指定玩家的记录');
            console.log('  node query_database.js stats         - 查询统计信息');
            console.log('\n示例:');
            console.log('  node query_database.js all');
            console.log('  node query_database.js top 5');
            console.log('  node query_database.js player 芳芳');
            console.log('  node query_database.js stats');
    }
}

// 运行主函数
main().catch(console.error);
