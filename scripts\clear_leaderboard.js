const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
    host: 'localhost',
    user: 'root',
    password: '123456',
    database: 'fangfang_game',
    charset: 'utf8mb4',
    port: 3306
};

/**
 * 清空排行榜数据
 */
async function clearLeaderboard() {
    console.log('🗑️  芳芳消消乐 - 清空排行榜工具');
    console.log('================================\n');
    
    try {
        const connection = await mysql.createConnection(dbConfig);
        
        // 1. 先查询当前记录数
        console.log('📊 查询当前记录数...');
        const [countBefore] = await connection.execute('SELECT COUNT(*) as count FROM leaderboard');
        console.log(`当前排行榜记录数: ${countBefore[0].count}条\n`);
        
        if (countBefore[0].count === 0) {
            console.log('✅ 排行榜已经是空的，无需清空');
            await connection.end();
            return;
        }
        
        // 2. 显示即将删除的记录
        console.log('📋 即将删除的记录预览:');
        const [records] = await connection.execute(`
            SELECT 
                player_name,
                score,
                COALESCE(game_type, 'match3') as game_type,
                DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:%s') as created_time
            FROM leaderboard 
            ORDER BY created_at DESC 
            LIMIT 5
        `);
        
        records.forEach((record, index) => {
            console.log(`  ${index + 1}. ${record.player_name} - ${record.score}分 (${record.game_type}) - ${record.created_time}`);
        });
        
        if (countBefore[0].count > 5) {
            console.log(`  ... 还有 ${countBefore[0].count - 5} 条记录`);
        }
        console.log();
        
        // 3. 确认删除
        console.log('⚠️  警告: 此操作将永久删除所有排行榜记录！');
        console.log('🔄 正在清空排行榜...');
        
        // 4. 执行清空操作
        await connection.execute('DELETE FROM leaderboard');
        
        // 5. 重置自增ID
        await connection.execute('ALTER TABLE leaderboard AUTO_INCREMENT = 1');
        
        // 6. 验证清空结果
        const [countAfter] = await connection.execute('SELECT COUNT(*) as count FROM leaderboard');
        
        console.log('✅ 排行榜清空完成！');
        console.log(`📊 清空前记录数: ${countBefore[0].count}条`);
        console.log(`📊 清空后记录数: ${countAfter[0].count}条`);
        console.log('🔄 自增ID已重置为1');
        
        await connection.end();
        
    } catch (error) {
        console.error('❌ 清空失败:', error.message);
        process.exit(1);
    }
}

/**
 * 备份排行榜数据（可选）
 */
async function backupLeaderboard() {
    console.log('💾 备份排行榜数据...');
    
    try {
        const connection = await mysql.createConnection(dbConfig);
        
        // 创建备份表
        const backupTableName = `leaderboard_backup_${new Date().toISOString().slice(0, 19).replace(/[-:]/g, '').replace('T', '_')}`;
        
        await connection.execute(`
            CREATE TABLE ${backupTableName} AS 
            SELECT * FROM leaderboard
        `);
        
        const [count] = await connection.execute(`SELECT COUNT(*) as count FROM ${backupTableName}`);
        console.log(`✅ 备份完成！备份表: ${backupTableName}`);
        console.log(`📊 备份记录数: ${count[0].count}条\n`);
        
        await connection.end();
        return backupTableName;
        
    } catch (error) {
        console.error('❌ 备份失败:', error.message);
        throw error;
    }
}

/**
 * 主函数
 */
async function main() {
    const args = process.argv.slice(2);
    const shouldBackup = args.includes('--backup') || args.includes('-b');
    const forceMode = args.includes('--force') || args.includes('-f');
    
    if (args.includes('--help') || args.includes('-h')) {
        console.log('🎮 芳芳消消乐 - 清空排行榜工具');
        console.log('================================');
        console.log('');
        console.log('用法:');
        console.log('  node clear_leaderboard.js              - 清空排行榜');
        console.log('  node clear_leaderboard.js --backup     - 先备份再清空');
        console.log('  node clear_leaderboard.js --force      - 强制清空（跳过确认）');
        console.log('  node clear_leaderboard.js -b -f        - 备份并强制清空');
        console.log('');
        console.log('选项:');
        console.log('  --backup, -b    清空前先备份数据');
        console.log('  --force, -f     跳过确认直接清空');
        console.log('  --help, -h      显示帮助信息');
        return;
    }
    
    try {
        // 备份数据（如果需要）
        if (shouldBackup) {
            await backupLeaderboard();
        }
        
        // 清空排行榜
        await clearLeaderboard();
        
        console.log('\n🎉 操作完成！');
        console.log('💡 提示: 游戏服务器会自动检测到数据变化');
        
    } catch (error) {
        console.error('\n❌ 操作失败:', error.message);
        process.exit(1);
    }
}

// 运行主函数
main().catch(console.error);
