<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>背景音乐测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        button {
            padding: 12px 24px;
            margin: 8px;
            border: none;
            border-radius: 8px;
            background: #4CAF50;
            color: white;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        .status {
            margin: 15px 0;
            padding: 15px;
            background: rgba(0,0,0,0.2);
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }
        .error {
            border-left-color: #f44336;
        }
        .info {
            border-left-color: #2196F3;
        }
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        .volume-control {
            margin: 20px 0;
        }
        .volume-control input {
            width: 100%;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 背景音乐测试</h1>
        
        <div class="status" id="status">
            点击"初始化音频"开始测试...
        </div>
        
        <div class="controls">
            <button onclick="initAudio()">🎵 初始化音频</button>
            <button onclick="playBackground()" id="playBtn" disabled>▶️ 播放背景音乐</button>
            <button onclick="pauseBackground()" id="pauseBtn" disabled>⏸️ 暂停</button>
            <button onclick="stopBackground()" id="stopBtn" disabled>⏹️ 停止</button>
            <button onclick="toggleMute()" id="muteBtn" disabled>🔇 静音</button>
            <button onclick="testDirectPlay()">🎧 直接测试MP3</button>
        </div>
        
        <div class="volume-control">
            <label for="volumeSlider">音量控制:</label>
            <input type="range" id="volumeSlider" min="0" max="100" value="30" 
                   oninput="setVolume(this.value)" disabled>
            <span id="volumeValue">30%</span>
        </div>
        
        <div class="status info">
            <h3>📋 测试说明:</h3>
            <ul>
                <li>首先点击"初始化音频"激活音频系统</li>
                <li>"播放背景音乐"会尝试播放真实MP3文件</li>
                <li>"直接测试MP3"会创建新的Audio对象测试</li>
                <li>如果MP3加载失败，会自动使用备用旋律</li>
                <li>查看状态信息了解详细情况</li>
            </ul>
        </div>
    </div>

    <script>
        let audioManager = null;
        let testAudio = null;
        let isMuted = false;
        
        function log(message, type = 'info') {
            const status = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'status error' : type === 'info' ? 'status info' : 'status';
            status.className = className;
            status.innerHTML = `<strong>${timestamp}:</strong> ${message}`;
            console.log(`[${timestamp}] ${message}`);
        }
        
        function initAudio() {
            try {
                // 创建简化的音频管理器用于测试
                audioManager = {
                    backgroundMusic: new Audio('/assets/audio/background.mp3'),
                    isMuted: false,
                    volume: 0.3
                };
                
                audioManager.backgroundMusic.loop = true;
                audioManager.backgroundMusic.volume = audioManager.volume;
                audioManager.backgroundMusic.preload = 'auto';
                
                // 添加事件监听器
                audioManager.backgroundMusic.addEventListener('loadstart', () => {
                    log('🎵 开始加载背景音乐...', 'info');
                });
                
                audioManager.backgroundMusic.addEventListener('loadeddata', () => {
                    log('📊 背景音乐数据加载完成', 'info');
                });
                
                audioManager.backgroundMusic.addEventListener('canplay', () => {
                    log('✅ 背景音乐可以播放', 'success');
                    enableControls();
                });
                
                audioManager.backgroundMusic.addEventListener('canplaythrough', () => {
                    log('✅ 背景音乐完全加载成功，可以流畅播放', 'success');
                });
                
                audioManager.backgroundMusic.addEventListener('error', (e) => {
                    log(`❌ 背景音乐加载失败: ${e.type} - ${audioManager.backgroundMusic.error?.message || '未知错误'}`, 'error');
                });
                
                audioManager.backgroundMusic.addEventListener('play', () => {
                    log('▶️ 背景音乐开始播放', 'success');
                });
                
                audioManager.backgroundMusic.addEventListener('pause', () => {
                    log('⏸️ 背景音乐暂停', 'info');
                });
                
                audioManager.backgroundMusic.addEventListener('ended', () => {
                    log('🔄 背景音乐播放结束（循环播放）', 'info');
                });
                
                // 强制开始加载
                audioManager.backgroundMusic.load();
                
                log('🎵 音频系统初始化完成，正在加载音乐文件...', 'info');
                
            } catch (error) {
                log(`❌ 音频初始化失败: ${error.message}`, 'error');
            }
        }
        
        function enableControls() {
            document.getElementById('playBtn').disabled = false;
            document.getElementById('pauseBtn').disabled = false;
            document.getElementById('stopBtn').disabled = false;
            document.getElementById('muteBtn').disabled = false;
            document.getElementById('volumeSlider').disabled = false;
        }
        
        function playBackground() {
            if (!audioManager) {
                log('❌ 请先初始化音频系统', 'error');
                return;
            }
            
            audioManager.backgroundMusic.play().catch(e => {
                log(`❌ 播放失败: ${e.message}`, 'error');
            });
        }
        
        function pauseBackground() {
            if (audioManager && audioManager.backgroundMusic) {
                audioManager.backgroundMusic.pause();
            }
        }
        
        function stopBackground() {
            if (audioManager && audioManager.backgroundMusic) {
                audioManager.backgroundMusic.pause();
                audioManager.backgroundMusic.currentTime = 0;
                log('⏹️ 背景音乐已停止', 'info');
            }
        }
        
        function toggleMute() {
            if (!audioManager) return;
            
            isMuted = !isMuted;
            audioManager.backgroundMusic.muted = isMuted;
            
            const muteBtn = document.getElementById('muteBtn');
            muteBtn.textContent = isMuted ? '🔊 取消静音' : '🔇 静音';
            
            log(isMuted ? '🔇 已静音' : '🔊 已取消静音', 'info');
        }
        
        function setVolume(value) {
            const volume = value / 100;
            document.getElementById('volumeValue').textContent = value + '%';
            
            if (audioManager && audioManager.backgroundMusic) {
                audioManager.backgroundMusic.volume = volume;
                log(`🔊 音量设置为 ${value}%`, 'info');
            }
        }
        
        function testDirectPlay() {
            log('🎧 创建新的Audio对象直接测试MP3文件...', 'info');
            
            if (testAudio) {
                testAudio.pause();
                testAudio = null;
            }
            
            testAudio = new Audio('/assets/audio/background.mp3');
            testAudio.volume = 0.5;
            
            testAudio.addEventListener('canplay', () => {
                log('✅ 直接测试：MP3文件可以播放', 'success');
                testAudio.play().catch(e => {
                    log(`❌ 直接测试播放失败: ${e.message}`, 'error');
                });
            });
            
            testAudio.addEventListener('error', (e) => {
                log(`❌ 直接测试加载失败: ${testAudio.error?.message || '未知错误'}`, 'error');
            });
            
            testAudio.addEventListener('play', () => {
                log('▶️ 直接测试：开始播放', 'success');
                setTimeout(() => {
                    testAudio.pause();
                    log('⏸️ 直接测试：3秒后自动停止', 'info');
                }, 3000);
            });
            
            testAudio.load();
        }
        
        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', () => {
            log('📱 页面加载完成，点击"初始化音频"开始测试', 'info');
        });
    </script>
</body>
</html>
