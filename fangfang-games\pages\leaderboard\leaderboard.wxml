<!-- 排行榜页面 -->
<view class="container">
  <!-- 页面标题 -->
  <view class="title">🏆 游戏排行榜</view>

  <!-- 游戏选择 -->
  <view class="game-card">
    <view class="p-20">
      <view class="subtitle" style="text-align: center; margin-bottom: 20rpx;">选择游戏</view>
      <view class="game-tabs">
        <button 
          class="tab-btn {{currentGame === 'all' ? 'active' : ''}}" 
          bindtap="switchGame" 
          data-game="all"
        >
          🎮 全部
        </button>
        <button 
          class="tab-btn {{currentGame === '连连看' ? 'active' : ''}}" 
          bindtap="switchGame" 
          data-game="连连看"
        >
          🔗 连连看
        </button>
        <button 
          class="tab-btn {{currentGame === '消消乐' ? 'active' : ''}}" 
          bindtap="switchGame" 
          data-game="消消乐"
        >
          💎 消消乐
        </button>
        <button 
          class="tab-btn {{currentGame === '俄罗斯方块' ? 'active' : ''}}" 
          bindtap="switchGame" 
          data-game="俄罗斯方块"
        >
          🧩 方块
        </button>
      </view>
    </view>
  </view>

  <!-- 排行榜列表 -->
  <view class="leaderboard-section">
    <view wx:if="{{leaderboard.length === 0}}" class="empty-state">
      <view style="font-size: 80rpx; margin-bottom: 20rpx;">🎯</view>
      <view style="font-size: 32rpx; color: white; margin-bottom: 10rpx;">暂无排行数据</view>
      <view style="font-size: 24rpx; color: rgba(255,255,255,0.8);">快去游戏中创造记录吧！</view>
    </view>

    <view wx:for="{{leaderboard}}" wx:key="index" class="rank-item">
      <view class="flex-between">
        <view class="flex" style="align-items: center;">
          <view class="rank-number">{{index + 1}}</view>
          <view style="margin-left: 20rpx;">
            <view class="player-name">{{item.username || '匿名玩家'}}</view>
            <view class="game-info">
              <text style="color: #666; font-size: 22rpx;">{{item.game}} • {{item.time}}</text>
            </view>
          </view>
        </view>
        <view class="score-display">
          <text class="score score-large">{{item.score}}</text>
          <text style="font-size: 20rpx; color: #666;">分</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 统计信息 -->
  <view class="stats-section">
    <view class="game-card">
      <view class="p-20">
        <view class="subtitle" style="text-align: center; margin-bottom: 20rpx;">📊 游戏统计</view>
        <view class="stats-grid">
          <view class="stat-item">
            <view class="stat-number">{{totalGames}}</view>
            <view class="stat-label">总游戏次数</view>
          </view>
          <view class="stat-item">
            <view class="stat-number">{{totalPlayers}}</view>
            <view class="stat-label">参与玩家</view>
          </view>
          <view class="stat-item">
            <view class="stat-number">{{highestScore}}</view>
            <view class="stat-label">最高分数</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="actions-section">
    <view class="game-card">
      <view class="p-20">
        <view class="flex" style="gap: 15rpx;">
          <button class="btn btn-primary" style="flex: 1;" bindtap="refreshData">
            🔄 刷新数据
          </button>
          <button class="btn btn-secondary" style="flex: 1;" bindtap="goToGames">
            🎮 返回游戏
          </button>
        </view>
      </view>
    </view>
  </view>
</view>
