// 排行榜页面
const app = getApp()

Page({
  data: {
    currentGame: 'all',
    leaderboard: [],
    totalGames: 0,
    totalPlayers: 0,
    highestScore: 0
  },

  onLoad() {
    console.log('🏆 排行榜页面加载完成')
  },

  onShow() {
    this.loadLeaderboard()
    this.loadStats()
  },

  // 切换游戏
  switchGame(e) {
    const game = e.currentTarget.dataset.game
    this.setData({ currentGame: game })
    this.loadLeaderboard()
  },

  // 加载排行榜数据
  loadLeaderboard() {
    try {
      const gameRecords = wx.getStorageSync('gameRecords') || []
      let filteredRecords = gameRecords

      // 按游戏类型过滤
      if (this.data.currentGame !== 'all') {
        filteredRecords = gameRecords.filter(record => record.game === this.data.currentGame)
      }

      // 按分数排序，取前20名
      const leaderboard = filteredRecords
        .sort((a, b) => b.score - a.score)
        .slice(0, 20)

      this.setData({ leaderboard })
    } catch (error) {
      console.error('加载排行榜失败:', error)
      this.setData({ leaderboard: [] })
    }
  },

  // 加载统计数据
  loadStats() {
    try {
      const gameRecords = wx.getStorageSync('gameRecords') || []
      
      // 计算统计数据
      const totalGames = gameRecords.length
      const uniquePlayers = [...new Set(gameRecords.map(r => r.username))].length
      const highestScore = gameRecords.length > 0 ? Math.max(...gameRecords.map(r => r.score)) : 0

      this.setData({
        totalGames,
        totalPlayers: uniquePlayers,
        highestScore
      })
    } catch (error) {
      console.error('加载统计数据失败:', error)
      this.setData({
        totalGames: 0,
        totalPlayers: 0,
        highestScore: 0
      })
    }
  },

  // 刷新数据
  refreshData() {
    wx.showLoading({
      title: '刷新中...'
    })

    setTimeout(() => {
      this.loadLeaderboard()
      this.loadStats()
      wx.hideLoading()
      
      wx.showToast({
        title: '刷新完成',
        icon: 'success'
      })
    }, 1000)
  },

  // 返回游戏
  goToGames() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  }
})
