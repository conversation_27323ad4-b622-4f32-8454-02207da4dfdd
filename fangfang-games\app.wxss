/* 芳芳的游戏世界 - 全局样式 */

page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.container {
  padding: 20rpx;
  min-height: 100vh;
}

/* 游戏卡片 */
.game-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
}

.game-card:active {
  transform: scale(0.98);
}

/* 按钮样式 */
.btn {
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  text-align: center;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #ff6b6b, #ff8e53);
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 107, 0.4);
}

.btn-secondary {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(78, 205, 196, 0.4);
}

.btn-success {
  background: linear-gradient(135deg, #56ab2f, #a8e6cf);
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(86, 171, 47, 0.4);
}

/* 文本样式 */
.title {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  text-align: center;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.3);
}

.subtitle {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 15rpx;
}

.game-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.game-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 分数样式 */
.score {
  color: #ff6b6b;
  font-weight: bold;
  font-size: 32rpx;
}

.score-large {
  font-size: 48rpx;
}

/* 布局样式 */
.flex {
  display: flex;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
  align-items: center;
}

.flex-column {
  flex-direction: column;
}

/* 间距 */
.p-20 { padding: 20rpx; }
.p-30 { padding: 30rpx; }
.mt-20 { margin-top: 20rpx; }
.mb-20 { margin-bottom: 20rpx; }

/* 游戏图标 */
.game-icon {
  font-size: 80rpx;
  margin-bottom: 15rpx;
  text-align: center;
}

/* 成就徽章 */
.achievement {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #333;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  margin: 5rpx;
  display: inline-block;
}

/* 排行榜样式 */
.rank-item {
  background: white;
  border-radius: 15rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.rank-number {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff6b6b, #ff8e53);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 24rpx;
}

/* 动画效果 */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10rpx);
  }
  60% {
    transform: translateY(-5rpx);
  }
}

.bounce {
  animation: bounce 1s infinite;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .container {
    padding: 15rpx;
  }
  
  .title {
    font-size: 40rpx;
  }
  
  .game-title {
    font-size: 32rpx;
  }
}
