<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        .container {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            background: #4CAF50;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #45a049;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            background: rgba(0,0,0,0.2);
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 音频系统测试</h1>
        
        <div class="status" id="status">
            等待测试...
        </div>
        
        <h3>Web Audio API 测试</h3>
        <button onclick="testBeep()">测试蜂鸣音</button>
        <button onclick="testMelody()">测试旋律</button>
        <button onclick="testAudioContext()">测试音频上下文</button>
        
        <h3>HTML5 Audio 测试</h3>
        <button onclick="testHTMLAudio()">测试HTML5音频</button>
        
        <h3>游戏音效测试</h3>
        <button onclick="testGameAudio()">测试游戏音效</button>
        <button onclick="testBackgroundMusic()">测试背景音乐</button>
        
        <h3>音频控制</h3>
        <button onclick="toggleMute()">切换静音</button>
        <button onclick="setVolume(0.1)">音量 10%</button>
        <button onclick="setVolume(0.5)">音量 50%</button>
        <button onclick="setVolume(1.0)">音量 100%</button>
    </div>

    <script>
        let audioContext;
        let audioManager;
        
        function log(message) {
            const status = document.getElementById('status');
            status.innerHTML += '<br>' + new Date().toLocaleTimeString() + ': ' + message;
            console.log(message);
        }
        
        function testAudioContext() {
            try {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                log('✅ AudioContext 创建成功，状态: ' + audioContext.state);
                
                if (audioContext.state === 'suspended') {
                    audioContext.resume().then(() => {
                        log('✅ AudioContext 已恢复');
                    });
                }
            } catch (error) {
                log('❌ AudioContext 创建失败: ' + error.message);
            }
        }
        
        function testBeep() {
            if (!audioContext) {
                testAudioContext();
            }
            
            try {
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.value = 800;
                oscillator.type = 'sine';
                
                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
                
                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.2);
                
                log('🔊 播放蜂鸣音 (800Hz, 0.2s)');
            } catch (error) {
                log('❌ 蜂鸣音播放失败: ' + error.message);
            }
        }
        
        function testMelody() {
            const frequencies = [523, 659, 784, 1047]; // C, E, G, C
            const noteDuration = 0.2;
            
            frequencies.forEach((freq, index) => {
                setTimeout(() => {
                    playNote(freq, noteDuration);
                }, index * noteDuration * 1000);
            });
            
            log('🎵 播放旋律 (C-E-G-C)');
        }
        
        function playNote(frequency, duration) {
            if (!audioContext) return;
            
            try {
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.value = frequency;
                oscillator.type = 'sine';
                
                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);
                
                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + duration);
            } catch (error) {
                log('❌ 音符播放失败: ' + error.message);
            }
        }
        
        function testHTMLAudio() {
            try {
                // 创建一个简单的数据URL音频
                const audio = new Audio();
                audio.volume = 0.5;
                
                // 尝试播放一个不存在的文件来测试错误处理
                audio.src = 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';
                
                audio.addEventListener('canplaythrough', () => {
                    log('✅ HTML5 Audio 可以播放');
                });
                
                audio.addEventListener('error', (e) => {
                    log('⚠️ HTML5 Audio 错误 (预期的): ' + e.type);
                });
                
                audio.play().catch(e => {
                    log('⚠️ HTML5 Audio 播放失败 (预期的): ' + e.message);
                });
                
            } catch (error) {
                log('❌ HTML5 Audio 测试失败: ' + error.message);
            }
        }
        
        function testGameAudio() {
            if (window.audioManager) {
                log('🎮 测试游戏音效...');
                window.audioManager.playSound('match');
                setTimeout(() => window.audioManager.playSound('click'), 200);
                setTimeout(() => window.audioManager.playSound('hint'), 400);
            } else {
                log('❌ 游戏音频管理器未找到');
            }
        }
        
        function testBackgroundMusic() {
            if (window.audioManager) {
                log('🎵 测试背景音乐...');
                window.audioManager.playBackgroundMusic();
            } else {
                log('❌ 游戏音频管理器未找到');
            }
        }
        
        function toggleMute() {
            if (window.audioManager) {
                const muted = window.audioManager.toggleMute();
                log(muted ? '🔇 已静音' : '🔊 已取消静音');
            } else {
                log('❌ 游戏音频管理器未找到');
            }
        }
        
        function setVolume(volume) {
            if (window.audioManager) {
                window.audioManager.setEffectVolume(volume);
                window.audioManager.setMusicVolume(volume);
                log('🔊 音量设置为 ' + (volume * 100) + '%');
            } else {
                log('❌ 游戏音频管理器未找到');
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('📱 页面加载完成');
            log('🌐 浏览器: ' + navigator.userAgent.split(' ').pop());
            log('🔊 音频支持: ' + (window.AudioContext ? '✅' : '❌'));
            
            // 检查是否有游戏音频管理器
            if (window.audioManager) {
                log('✅ 游戏音频管理器已加载');
            } else {
                log('⚠️ 游戏音频管理器未加载，请在游戏页面中测试');
            }
        });
        
        // 用户交互后自动测试
        document.addEventListener('click', function testOnFirstClick() {
            log('👆 检测到用户交互，开始自动测试...');
            setTimeout(testAudioContext, 100);
            setTimeout(testBeep, 500);
            
            // 移除监听器
            document.removeEventListener('click', testOnFirstClick);
        }, { once: true });
    </script>
</body>
</html>
