/* 排行榜页面样式 */

.game-tabs {
  display: flex;
  gap: 10rpx;
  justify-content: center;
  flex-wrap: wrap;
}

.tab-btn {
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e9ecef;
  border-radius: 30rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.tab-btn.active {
  background: linear-gradient(135deg, #ff6b6b, #ff8e53);
  color: white;
  border-color: #ff6b6b;
  box-shadow: 0 4rpx 15rpx rgba(255, 107, 107, 0.3);
}

.leaderboard-section {
  margin-bottom: 30rpx;
}

.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
}

.player-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.game-info {
  font-size: 22rpx;
  color: #666;
}

.score-display {
  text-align: right;
}

.stats-section {
  margin-bottom: 30rpx;
}

.stats-grid {
  display: flex;
  justify-content: space-around;
  text-align: center;
}

.stat-item {
  flex: 1;
}

.stat-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff6b6b;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #666;
}

.actions-section {
  margin-bottom: 40rpx;
}
