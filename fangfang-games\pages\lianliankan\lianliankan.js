// 连连看游戏
const app = getApp()

Page({
  data: {
    gameStarted: false,
    selectedTime: 30,
    timeLeft: 30,
    score: 0,
    hintsLeft: 2,
    gameBoard: [],
    selectedCells: [],
    eliminatedPairs: 0,
    showGameOver: false,
    showPause: false,
    gameWon: false,
    timer: null,
    emojis: ['🍎', '🍌', '🍇', '🍓', '🥝', '🍑', '🍒', '🥭', '🍍', '🥥', '🍊', '🍋']
  },

  onLoad() {
    console.log('🔗 连连看游戏加载完成')
  },

  onUnload() {
    this.clearTimer()
  },

  // 选择游戏时间
  selectTime(e) {
    const time = parseInt(e.currentTarget.dataset.time)
    const hintsLeft = time === 30 ? 2 : 4
    
    this.setData({
      selectedTime: time,
      timeLeft: time,
      hintsLeft: hintsLeft
    })
  },

  // 开始游戏
  startGame() {
    this.initGameBoard()
    this.setData({
      gameStarted: true,
      score: 0,
      eliminatedPairs: 0,
      selectedCells: [],
      showGameOver: false,
      gameWon: false
    })
    this.startTimer()
  },

  // 初始化游戏棋盘
  initGameBoard() {
    const rows = 6
    const cols = 8
    const totalCells = rows * cols
    const pairCount = totalCells / 2
    
    // 生成配对的emoji
    const gameEmojis = []
    for (let i = 0; i < pairCount; i++) {
      const emoji = this.data.emojis[i % this.data.emojis.length]
      gameEmojis.push(emoji, emoji)
    }
    
    // 打乱数组
    this.shuffleArray(gameEmojis)
    
    // 创建棋盘
    const gameBoard = []
    for (let row = 0; row < rows; row++) {
      const boardRow = []
      for (let col = 0; col < cols; col++) {
        const index = row * cols + col
        boardRow.push({
          emoji: gameEmojis[index],
          matched: false,
          selected: false,
          row: row,
          col: col
        })
      }
      gameBoard.push(boardRow)
    }
    
    this.setData({ gameBoard })
  },

  // 打乱数组
  shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]]
    }
  },

  // 开始计时
  startTimer() {
    this.clearTimer()
    this.data.timer = setInterval(() => {
      const timeLeft = this.data.timeLeft - 1
      this.setData({ timeLeft })
      
      if (timeLeft <= 0) {
        this.gameOver(false)
      }
    }, 1000)
  },

  // 清除计时器
  clearTimer() {
    if (this.data.timer) {
      clearInterval(this.data.timer)
      this.data.timer = null
    }
  },

  // 选择方块
  selectCell(e) {
    const { row, col } = e.currentTarget.dataset
    const cell = this.data.gameBoard[row][col]
    
    if (cell.matched || cell.selected) return
    
    const selectedCells = [...this.data.selectedCells]
    
    if (selectedCells.length === 0) {
      // 选择第一个方块
      selectedCells.push({ row, col })
      this.updateCellSelection(row, col, true)
    } else if (selectedCells.length === 1) {
      // 选择第二个方块
      const firstCell = selectedCells[0]
      
      if (firstCell.row === row && firstCell.col === col) {
        // 取消选择
        selectedCells.pop()
        this.updateCellSelection(row, col, false)
      } else {
        selectedCells.push({ row, col })
        this.updateCellSelection(row, col, true)
        
        // 检查是否匹配
        setTimeout(() => {
          this.checkMatch(selectedCells)
        }, 300)
      }
    } else {
      // 重新选择
      this.clearSelection()
      selectedCells.length = 0
      selectedCells.push({ row, col })
      this.updateCellSelection(row, col, true)
    }
    
    this.setData({ selectedCells })
  },

  // 更新方块选择状态
  updateCellSelection(row, col, selected) {
    const gameBoard = this.data.gameBoard
    gameBoard[row][col].selected = selected
    this.setData({ gameBoard })
  },

  // 清除选择
  clearSelection() {
    const gameBoard = this.data.gameBoard
    gameBoard.forEach(row => {
      row.forEach(cell => {
        cell.selected = false
      })
    })
    this.setData({ gameBoard })
  },

  // 检查匹配
  checkMatch(selectedCells) {
    const [first, second] = selectedCells
    const firstCell = this.data.gameBoard[first.row][first.col]
    const secondCell = this.data.gameBoard[second.row][second.col]
    
    if (firstCell.emoji === secondCell.emoji && this.canConnect(first, second)) {
      // 匹配成功
      this.eliminatePair(first, second)
    } else {
      // 匹配失败
      this.clearSelection()
      this.setData({ selectedCells: [] })
    }
  },

  // 检查是否可以连接
  canConnect(pos1, pos2) {
    // 简化版连接检查：直线连接或一次转弯
    const { row: r1, col: c1 } = pos1
    const { row: r2, col: c2 } = pos2
    
    // 直线连接
    if (r1 === r2 || c1 === c2) {
      return this.isPathClear(pos1, pos2)
    }
    
    // 一次转弯连接
    const corner1 = { row: r1, col: c2 }
    const corner2 = { row: r2, col: c1 }
    
    if (!this.data.gameBoard[corner1.row][corner1.col].matched &&
        this.isPathClear(pos1, corner1) && this.isPathClear(corner1, pos2)) {
      return true
    }
    
    if (!this.data.gameBoard[corner2.row][corner2.col].matched &&
        this.isPathClear(pos1, corner2) && this.isPathClear(corner2, pos2)) {
      return true
    }
    
    return false
  },

  // 检查路径是否畅通
  isPathClear(pos1, pos2) {
    const { row: r1, col: c1 } = pos1
    const { row: r2, col: c2 } = pos2
    
    if (r1 === r2) {
      // 水平路径
      const minCol = Math.min(c1, c2)
      const maxCol = Math.max(c1, c2)
      for (let col = minCol + 1; col < maxCol; col++) {
        if (!this.data.gameBoard[r1][col].matched) return false
      }
    } else if (c1 === c2) {
      // 垂直路径
      const minRow = Math.min(r1, r2)
      const maxRow = Math.max(r1, r2)
      for (let row = minRow + 1; row < maxRow; row++) {
        if (!this.data.gameBoard[row][c1].matched) return false
      }
    }
    
    return true
  },

  // 消除配对
  eliminatePair(pos1, pos2) {
    const gameBoard = this.data.gameBoard
    gameBoard[pos1.row][pos1.col].matched = true
    gameBoard[pos1.row][pos1.col].selected = false
    gameBoard[pos2.row][pos2.col].matched = true
    gameBoard[pos2.row][pos2.col].selected = false
    
    const score = this.data.score + 100
    const eliminatedPairs = this.data.eliminatedPairs + 1
    
    this.setData({
      gameBoard,
      selectedCells: [],
      score,
      eliminatedPairs
    })
    
    // 检查是否获胜
    if (this.checkWin()) {
      this.gameOver(true)
    }
  },

  // 检查是否获胜
  checkWin() {
    return this.data.gameBoard.every(row => 
      row.every(cell => cell.matched)
    )
  },

  // 使用提示
  useHint() {
    if (this.data.hintsLeft <= 0) {
      wx.showToast({
        title: '提示次数已用完',
        icon: 'none'
      })
      return
    }
    
    // 简单提示：高亮一对可消除的方块
    const hint = this.findHint()
    if (hint) {
      const { pos1, pos2 } = hint
      this.updateCellSelection(pos1.row, pos1.col, true)
      this.updateCellSelection(pos2.row, pos2.col, true)
      
      setTimeout(() => {
        this.clearSelection()
      }, 2000)
      
      this.setData({
        hintsLeft: this.data.hintsLeft - 1
      })
    } else {
      wx.showToast({
        title: '没有可消除的配对',
        icon: 'none'
      })
    }
  },

  // 寻找提示
  findHint() {
    const gameBoard = this.data.gameBoard
    
    for (let r1 = 0; r1 < gameBoard.length; r1++) {
      for (let c1 = 0; c1 < gameBoard[r1].length; c1++) {
        if (gameBoard[r1][c1].matched) continue
        
        for (let r2 = 0; r2 < gameBoard.length; r2++) {
          for (let c2 = 0; c2 < gameBoard[r2].length; c2++) {
            if (gameBoard[r2][c2].matched) continue
            if (r1 === r2 && c1 === c2) continue
            
            if (gameBoard[r1][c1].emoji === gameBoard[r2][c2].emoji &&
                this.canConnect({ row: r1, col: c1 }, { row: r2, col: c2 })) {
              return {
                pos1: { row: r1, col: c1 },
                pos2: { row: r2, col: c2 }
              }
            }
          }
        }
      }
    }
    
    return null
  },

  // 暂停游戏
  pauseGame() {
    this.clearTimer()
    this.setData({ showPause: true })
  },

  // 恢复游戏
  resumeGame() {
    this.setData({ showPause: false })
    this.startTimer()
  },

  // 重新开始
  restartGame() {
    this.clearTimer()
    this.setData({
      timeLeft: this.data.selectedTime,
      hintsLeft: this.data.selectedTime === 30 ? 2 : 4
    })
    this.startGame()
  },

  // 游戏结束
  gameOver(won) {
    this.clearTimer()
    
    if (won) {
      // 计算奖励分数
      const timeBonus = this.data.timeLeft * 10
      const finalScore = this.data.score + timeBonus
      
      this.setData({
        score: finalScore,
        gameWon: true
      })
      
      // 保存分数
      app.updateScore('连连看', finalScore)
      app.checkAchievements()
    }
    
    this.setData({ showGameOver: true })
  },

  // 再玩一次
  playAgain() {
    this.setData({
      showGameOver: false,
      gameStarted: false,
      timeLeft: this.data.selectedTime,
      hintsLeft: this.data.selectedTime === 30 ? 2 : 4
    })
  },

  // 返回首页
  backToHome() {
    wx.navigateBack()
  }
})
