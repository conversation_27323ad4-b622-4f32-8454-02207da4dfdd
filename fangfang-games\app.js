// 芳芳的游戏世界
App({
  onLaunch() {
    console.log('🎮 芳芳的游戏世界启动')
    this.initUserData()
  },

  // 初始化用户数据
  initUserData() {
    try {
      const userData = wx.getStorageSync('userData') || {
        username: '',
        totalScore: 0,
        gamesPlayed: 0,
        achievements: []
      }
      this.globalData.userData = userData
    } catch (error) {
      console.error('初始化用户数据失败:', error)
    }
  },

  globalData: {
    userData: {
      username: '',
      totalScore: 0,
      gamesPlayed: 0,
      achievements: []
    },
    currentGame: null
  },

  // 保存用户数据
  saveUserData() {
    try {
      wx.setStorageSync('userData', this.globalData.userData)
    } catch (error) {
      console.error('保存用户数据失败:', error)
    }
  },

  // 设置用户名
  setUsername(username) {
    this.globalData.userData.username = username
    this.saveUserData()
  },

  // 更新游戏分数
  updateScore(gameName, score) {
    try {
      // 更新总分
      this.globalData.userData.totalScore += score
      this.globalData.userData.gamesPlayed += 1

      // 保存游戏记录
      const gameRecords = wx.getStorageSync('gameRecords') || []
      gameRecords.unshift({
        game: gameName,
        score: score,
        time: new Date().toLocaleString(),
        username: this.globalData.userData.username
      })

      // 只保留最近100条记录
      if (gameRecords.length > 100) {
        gameRecords.splice(100)
      }

      wx.setStorageSync('gameRecords', gameRecords)
      this.saveUserData()

      console.log(`🎯 ${gameName} 得分: ${score}`)
    } catch (error) {
      console.error('更新分数失败:', error)
    }
  },

  // 获取游戏排行榜
  getLeaderboard(gameName) {
    try {
      const gameRecords = wx.getStorageSync('gameRecords') || []
      const gameScores = gameRecords
        .filter(record => record.game === gameName)
        .sort((a, b) => b.score - a.score)
        .slice(0, 10) // 取前10名

      return gameScores
    } catch (error) {
      console.error('获取排行榜失败:', error)
      return []
    }
  },

  // 检查成就
  checkAchievements() {
    const userData = this.globalData.userData
    const achievements = []

    // 首次游戏
    if (userData.gamesPlayed === 1) {
      achievements.push('🎮 初次体验')
    }

    // 游戏达人
    if (userData.gamesPlayed >= 10) {
      achievements.push('🏆 游戏达人')
    }

    // 高分玩家
    if (userData.totalScore >= 1000) {
      achievements.push('⭐ 高分玩家')
    }

    // 更新成就
    achievements.forEach(achievement => {
      if (!userData.achievements.includes(achievement)) {
        userData.achievements.push(achievement)
        wx.showToast({
          title: `获得成就: ${achievement}`,
          icon: 'success'
        })
      }
    })

    this.saveUserData()
  }
})
