// 芳芳的游戏世界主页
const app = getApp()

Page({
  data: {
    userData: {},
    inputUsername: '',
    lianliankanBest: 0,
    xiaoxiaoleBest: 0,
    tetrisBest: 0
  },

  onLoad() {
    console.log('🎮 游戏世界主页加载完成')
  },

  onShow() {
    this.loadUserData()
    this.loadBestScores()
  },

  // 加载用户数据
  loadUserData() {
    const userData = app.globalData.userData
    this.setData({ userData })
  },

  // 加载最高分数
  loadBestScores() {
    try {
      const gameRecords = wx.getStorageSync('gameRecords') || []
      
      // 获取各游戏最高分
      const lianliankanScores = gameRecords.filter(r => r.game === '连连看').map(r => r.score)
      const xiaoxiaoleScores = gameRecords.filter(r => r.game === '消消乐').map(r => r.score)
      const tetrisScores = gameRecords.filter(r => r.game === '俄罗斯方块').map(r => r.score)

      this.setData({
        lianliankanBest: lianliankanScores.length > 0 ? Math.max(...lianliankanScores) : 0,
        xiaoxiaoleBest: xiaoxiaoleScores.length > 0 ? Math.max(...xiaoxiaoleScores) : 0,
        tetrisBest: tetrisScores.length > 0 ? Math.max(...tetrisScores) : 0
      })
    } catch (error) {
      console.error('加载最高分失败:', error)
    }
  },

  // 用户名输入
  onUsernameInput(e) {
    this.setData({
      inputUsername: e.detail.value
    })
  },

  // 设置用户名
  setUsername() {
    const username = this.data.inputUsername.trim()
    
    if (!username) {
      wx.showToast({
        title: '请输入用户名',
        icon: 'none'
      })
      return
    }

    if (username.length > 10) {
      wx.showToast({
        title: '用户名不能超过10个字符',
        icon: 'none'
      })
      return
    }

    app.setUsername(username)
    this.loadUserData()

    wx.showToast({
      title: '欢迎来到游戏世界！',
      icon: 'success'
    })
  },

  // 更换用户名
  changeUsername() {
    wx.showModal({
      title: '更换用户名',
      content: '确定要更换用户名吗？这不会影响您的游戏记录。',
      success: (res) => {
        if (res.confirm) {
          app.setUsername('')
          this.setData({
            userData: app.globalData.userData,
            inputUsername: ''
          })
        }
      }
    })
  },

  // 开始游戏
  startGame(e) {
    const game = e.currentTarget.dataset.game
    
    wx.showLoading({
      title: '启动游戏中...'
    })

    setTimeout(() => {
      wx.hideLoading()
      
      switch(game) {
        case 'lianliankan':
          wx.navigateTo({
            url: '/pages/lianliankan/lianliankan'
          })
          break
        case 'xiaoxiaole':
          wx.navigateTo({
            url: '/pages/xiaoxiaole/xiaoxiaole'
          })
          break
        case 'tetris':
          wx.navigateTo({
            url: '/pages/tetris/tetris'
          })
          break
        default:
          wx.showToast({
            title: '游戏开发中...',
            icon: 'none'
          })
      }
    }, 1000)
  },

  // 查看排行榜
  viewLeaderboard() {
    wx.switchTab({
      url: '/pages/leaderboard/leaderboard'
    })
  },

  // 清除数据
  clearData() {
    wx.showModal({
      title: '清除数据',
      content: '确定要清除所有游戏数据吗？此操作不可恢复！',
      confirmText: '确定清除',
      confirmColor: '#ff6b6b',
      success: (res) => {
        if (res.confirm) {
          try {
            wx.clearStorageSync()
            app.initUserData()
            this.loadUserData()
            this.loadBestScores()
            
            wx.showToast({
              title: '数据已清除',
              icon: 'success'
            })
          } catch (error) {
            console.error('清除数据失败:', error)
            wx.showToast({
              title: '清除失败',
              icon: 'none'
            })
          }
        }
      }
    })
  }
})
