# 李子奇 - 个人简历

## 📋 基本信息
- **姓名**：李子奇
- **性别**：男/女
- **年龄**：XX岁
- **联系电话**：+86 XXX-XXXX-XXXX
- **邮箱**：<EMAIL>
- **现居地址**：北京市/上海市/深圳市等
- **求职意向**：软件开发工程师/前端工程师/全栈工程师

---

## 🎓 教育背景
### XX大学 | 计算机科学与技术专业 | 本科/硕士
**时间**：20XX年9月 - 20XX年6月  
**主要课程**：数据结构、算法设计、数据库系统、软件工程、计算机网络  
**GPA**：X.X/4.0 (专业排名前X%)  
**获奖情况**：
- 优秀学生奖学金（连续X年）
- XX编程竞赛X等奖
- 优秀毕业设计

---

## 💼 工作经历

### XX科技有限公司 | 软件开发工程师
**时间**：20XX年X月 - 至今  
**主要职责**：
- 负责公司核心产品的前端开发，使用React/Vue.js技术栈
- 参与系统架构设计，优化代码性能，提升用户体验
- 与产品经理、UI设计师协作，完成产品需求开发
- 指导初级开发人员，参与代码审查和技术分享

**主要成就**：
- 主导开发的XX项目，用户量增长XX%，获得公司年度最佳项目奖
- 优化系统性能，页面加载速度提升XX%
- 建立前端开发规范，提高团队开发效率XX%

### XX互联网公司 | 前端开发实习生
**时间**：20XX年X月 - 20XX年X月  
**主要职责**：
- 参与公司官网和管理后台的开发维护
- 学习并应用现代前端开发技术和工具
- 协助完成移动端H5页面开发

---

## 🛠️ 技能特长

### 编程语言
- **JavaScript/TypeScript**：熟练掌握ES6+语法，理解异步编程
- **HTML5/CSS3**：精通语义化标签，熟悉响应式设计
- **Python**：熟悉Django/Flask框架，能够进行后端开发
- **Java**：了解Spring Boot框架，具备基础开发能力

### 前端技术
- **框架**：React、Vue.js、Angular（熟练使用其中2-3个）
- **状态管理**：Redux、Vuex、MobX
- **构建工具**：Webpack、Vite、Rollup
- **CSS预处理器**：Sass、Less、Stylus
- **UI框架**：Ant Design、Element UI、Material-UI

### 后端技术
- **Node.js**：Express、Koa框架开发经验
- **数据库**：MySQL、MongoDB、Redis
- **服务器**：Linux基础操作，Nginx配置
- **云服务**：阿里云、腾讯云部署经验

### 开发工具
- **版本控制**：Git、SVN
- **开发环境**：VS Code、WebStorm
- **项目管理**：Jira、Trello、禅道
- **设计工具**：Figma、Sketch基础使用

---

## 🏆 项目经验

### 芳芳消消乐游戏平台
**项目时间**：20XX年X月 - 20XX年X月  
**项目描述**：基于Web技术开发的多人在线休闲游戏平台，包含消消乐、连连看、俄罗斯方块等多种游戏模式。

**技术栈**：
- 前端：HTML5 Canvas、JavaScript ES6+、CSS3动画
- 后端：Node.js + Express + MySQL
- 部署：Docker + Nginx

**主要职责**：
- 负责游戏核心逻辑开发，实现消除算法和动画效果
- 设计并实现排行榜系统，支持多种游戏模式数据统计
- 优化游戏性能，确保流畅的用户体验
- 实现响应式设计，支持PC和移动端访问

**项目成果**：
- 游戏平台日活跃用户达到XXX人
- 游戏加载速度优化至X秒内
- 获得用户好评率XX%以上

### XX管理系统
**项目时间**：20XX年X月 - 20XX年X月  
**项目描述**：企业内部管理系统，包含用户管理、权限控制、数据统计等功能。

**技术栈**：React + Ant Design + Node.js + MySQL

**主要职责**：
- 负责前端页面开发和交互逻辑实现
- 参与数据库设计和API接口开发
- 实现用户权限管理和数据安全控制

---

## 🌟 个人特点
- **学习能力强**：能够快速掌握新技术，持续关注行业发展趋势
- **团队协作**：具备良好的沟通能力，能够与不同角色的同事高效协作
- **问题解决**：具备较强的逻辑思维和问题分析能力
- **责任心强**：对工作认真负责，能够按时保质完成任务
- **创新思维**：喜欢尝试新技术，能够提出创新性的解决方案

---

## 🎯 职业规划
- **短期目标**：在前端/全栈开发领域深入发展，提升技术深度和广度
- **中期目标**：成为技术专家，能够独立负责大型项目的技术架构
- **长期目标**：向技术管理方向发展，带领团队完成更有挑战性的项目

---

## 📞 联系方式
- **手机**：+86 XXX-XXXX-XXXX
- **邮箱**：<EMAIL>
- **GitHub**：https://github.com/liziqi
- **个人博客**：https://liziqi.blog.com
- **LinkedIn**：https://linkedin.com/in/liziqi

---

*简历最后更新时间：20XX年X月*
