<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频文件检查</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        .container {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .file-check {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: rgba(0,0,0,0.2);
            border-radius: 5px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
        }
        .exists { background: #4CAF50; }
        .missing { background: #f44336; }
        .checking { background: #FF9800; }
        button {
            padding: 10px 20px;
            margin: 10px 5px;
            border: none;
            border-radius: 5px;
            background: #4CAF50;
            color: white;
            cursor: pointer;
        }
        button:hover { background: #45a049; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 音频文件检查工具</h1>
        
        <p>检查游戏所需的音频文件是否存在：</p>
        
        <div id="fileList">
            <!-- 文件列表将在这里动态生成 -->
        </div>
        
        <button onclick="checkAllFiles()">🔍 检查所有文件</button>
        <button onclick="downloadMissingFiles()">📥 下载缺失文件信息</button>
        
        <div id="summary" style="margin-top: 20px; padding: 15px; background: rgba(0,0,0,0.2); border-radius: 8px;">
            <h3>📊 检查结果：</h3>
            <p id="summaryText">点击"检查所有文件"开始检查...</p>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: rgba(0,0,0,0.2); border-radius: 8px;">
            <h3>💡 说明：</h3>
            <ul>
                <li><strong>background.mp3</strong>: 背景音乐文件（已存在）</li>
                <li><strong>其他音效</strong>: 使用Web Audio API生成，无需文件</li>
                <li><strong>绿色</strong>: 文件存在且可以加载</li>
                <li><strong>红色</strong>: 文件不存在（使用备用音效）</li>
                <li><strong>橙色</strong>: 正在检查中</li>
            </ul>
        </div>
    </div>

    <script>
        const audioFiles = [
            { name: 'background.mp3', description: '背景音乐', required: true },
            { name: 'match.mp3', description: '匹配成功音效', required: false },
            { name: 'swap.mp3', description: '交换音效', required: false },
            { name: 'click.mp3', description: '点击音效', required: false },
            { name: 'hint.mp3', description: '提示音效', required: false },
            { name: 'game-start.mp3', description: '游戏开始音效', required: false },
            { name: 'game-win.mp3', description: '游戏胜利音效', required: false },
            { name: 'game-over.mp3', description: '游戏结束音效', required: false },
            { name: 'button-hover.mp3', description: '按钮悬停音效', required: false },
            { name: 'button-click.mp3', description: '按钮点击音效', required: false },
            { name: 'popup.mp3', description: '弹窗音效', required: false },
            { name: 'connect.mp3', description: '连线成功音效', required: false },
            { name: 'eliminate.mp3', description: '消除音效', required: false },
            { name: 'combo.mp3', description: '连击音效', required: false },
            { name: 'cascade.mp3', description: '连锁音效', required: false }
        ];

        function createFileList() {
            const fileList = document.getElementById('fileList');
            fileList.innerHTML = '';
            
            audioFiles.forEach(file => {
                const div = document.createElement('div');
                div.className = 'file-check';
                div.innerHTML = `
                    <span>
                        <strong>${file.name}</strong> - ${file.description}
                        ${file.required ? '<em>(必需)</em>' : '<em>(可选)</em>'}
                    </span>
                    <span class="status checking" id="status-${file.name}">检查中...</span>
                `;
                fileList.appendChild(div);
            });
        }

        function checkFile(fileName) {
            return new Promise((resolve) => {
                const audio = new Audio(`/assets/audio/${fileName}`);
                
                const timeout = setTimeout(() => {
                    resolve(false);
                }, 3000);
                
                audio.addEventListener('canplaythrough', () => {
                    clearTimeout(timeout);
                    resolve(true);
                });
                
                audio.addEventListener('error', () => {
                    clearTimeout(timeout);
                    resolve(false);
                });
                
                audio.load();
            });
        }

        async function checkAllFiles() {
            let existsCount = 0;
            let missingCount = 0;
            let requiredMissing = 0;
            
            for (const file of audioFiles) {
                const statusElement = document.getElementById(`status-${file.name}`);
                statusElement.textContent = '检查中...';
                statusElement.className = 'status checking';
                
                const exists = await checkFile(file.name);
                
                if (exists) {
                    statusElement.textContent = '✅ 存在';
                    statusElement.className = 'status exists';
                    existsCount++;
                } else {
                    statusElement.textContent = file.required ? '❌ 缺失' : '⚠️ 缺失(使用备用)';
                    statusElement.className = 'status missing';
                    missingCount++;
                    if (file.required) requiredMissing++;
                }
            }
            
            const summaryText = document.getElementById('summaryText');
            summaryText.innerHTML = `
                <strong>检查完成！</strong><br>
                ✅ 存在: ${existsCount} 个文件<br>
                ❌ 缺失: ${missingCount} 个文件<br>
                ${requiredMissing > 0 ? 
                    `<span style="color: #f44336;">⚠️ 必需文件缺失: ${requiredMissing} 个</span>` : 
                    '<span style="color: #4CAF50;">✅ 所有必需文件都存在</span>'
                }<br><br>
                <em>注意：缺失的音效文件会自动使用Web Audio API生成的备用音效</em>
            `;
        }

        function downloadMissingFiles() {
            const missingFiles = audioFiles.filter(async (file) => {
                return !(await checkFile(file.name));
            });
            
            const info = `
# 缺失的音频文件列表

游戏需要以下音频文件，但当前不存在：

${audioFiles.map(file => `
## ${file.name}
- **描述**: ${file.description}
- **必需**: ${file.required ? '是' : '否'}
- **路径**: assets/audio/${file.name}
- **格式**: MP3
- **建议时长**: ${getSuggestedDuration(file.name)}
`).join('\n')}

## 解决方案

1. **使用现有系统**: 游戏会自动使用Web Audio API生成备用音效
2. **添加真实音效**: 将MP3文件放入 assets/audio/ 目录
3. **推荐网站**: 
   - Freesound.org (免费音效)
   - Zapsplat.com (高质量音效)
   - YouTube Audio Library (免费音乐)

## 注意事项

- 只有 background.mp3 是必需的（已存在）
- 其他音效都有备用方案
- 音效文件应该简短（0.1-2秒）
- 建议进行音量标准化处理
            `;
            
            const blob = new Blob([info], { type: 'text/markdown' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'missing-audio-files.md';
            a.click();
            URL.revokeObjectURL(url);
        }

        function getSuggestedDuration(fileName) {
            if (fileName.includes('background')) return '2-5分钟';
            if (fileName.includes('game-start') || fileName.includes('game-win')) return '1-3秒';
            if (fileName.includes('hover')) return '0.1-0.2秒';
            return '0.2-1秒';
        }

        // 页面加载时创建文件列表
        document.addEventListener('DOMContentLoaded', createFileList);
    </script>
</body>
</html>
