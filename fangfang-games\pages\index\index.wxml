<!-- 芳芳的游戏世界主页 -->
<view class="container">
  <!-- 欢迎标题 -->
  <view class="welcome-section">
    <view class="title">🎮 芳芳的游戏世界</view>
    <view style="text-align: center; color: white; font-size: 28rpx; margin-bottom: 40rpx;">
      欢迎来到充满乐趣的游戏天地
    </view>
  </view>

  <!-- 用户信息卡片 -->
  <view class="game-card" wx:if="{{!userData.username}}">
    <view class="p-30" style="text-align: center;">
      <view class="game-icon">👤</view>
      <view class="game-title">设置用户名</view>
      <view class="game-desc mb-20">请输入您的游戏昵称</view>
      <input 
        class="username-input" 
        placeholder="请输入用户名" 
        value="{{inputUsername}}"
        bindinput="onUsernameInput"
        style="border: 2rpx solid #ddd; border-radius: 10rpx; padding: 15rpx; margin-bottom: 20rpx; background: white;"
      />
      <button class="btn btn-primary" bindtap="setUsername">开始游戏</button>
    </view>
  </view>

  <!-- 用户状态卡片 -->
  <view class="game-card" wx:if="{{userData.username}}">
    <view class="p-20">
      <view class="flex-between">
        <view>
          <view class="game-title">👋 你好，{{userData.username}}</view>
          <view class="game-desc">总分数: {{userData.totalScore}} | 游戏次数: {{userData.gamesPlayed}}</view>
        </view>
        <button class="btn btn-secondary" style="padding: 10rpx 20rpx; font-size: 24rpx;" bindtap="changeUsername">
          更换用户名
        </button>
      </view>
      
      <!-- 成就展示 -->
      <view wx:if="{{userData.achievements.length > 0}}" style="margin-top: 15rpx;">
        <view style="font-size: 24rpx; color: #666; margin-bottom: 10rpx;">🏆 我的成就:</view>
        <view>
          <text class="achievement" wx:for="{{userData.achievements}}" wx:key="*this">{{item}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 游戏列表 -->
  <view class="games-section" wx:if="{{userData.username}}">
    <!-- 连连看游戏 -->
    <view class="game-card" bindtap="startGame" data-game="lianliankan">
      <view class="p-30">
        <view class="flex-between">
          <view style="flex: 1;">
            <view class="game-icon">🔗</view>
            <view class="game-title">芳芳连连看</view>
            <view class="game-desc">
              经典连连看游戏，消除相同图案<br/>
              ⏱️ 时间模式：30秒/60秒<br/>
              💡 提示系统：2次/4次<br/>
              🎯 目标：消除所有图案
            </view>
          </view>
          <view class="play-btn">
            <text style="font-size: 60rpx;">▶️</text>
          </view>
        </view>
        <view class="game-stats mt-20">
          <text class="score">最高分: {{lianliankanBest}}</text>
        </view>
      </view>
    </view>

    <!-- 消消乐游戏 -->
    <view class="game-card" bindtap="startGame" data-game="xiaoxiaole">
      <view class="p-30">
        <view class="flex-between">
          <view style="flex: 1;">
            <view class="game-icon">💎</view>
            <view class="game-title">芳芳消消乐</view>
            <view class="game-desc">
              三消类益智游戏，连接相同元素<br/>
              🎮 玩法：连接3个或以上相同图案<br/>
              ⚡ 特效：连击获得额外分数<br/>
              🏆 挑战：达到目标分数过关
            </view>
          </view>
          <view class="play-btn">
            <text style="font-size: 60rpx;">▶️</text>
          </view>
        </view>
        <view class="game-stats mt-20">
          <text class="score">最高分: {{xiaoxiaoleBest}}</text>
        </view>
      </view>
    </view>

    <!-- 俄罗斯方块游戏 -->
    <view class="game-card" bindtap="startGame" data-game="tetris">
      <view class="p-30">
        <view class="flex-between">
          <view style="flex: 1;">
            <view class="game-icon">🧩</view>
            <view class="game-title">芳芳俄罗斯方块</view>
            <view class="game-desc">
              经典俄罗斯方块，永恒的挑战<br/>
              📐 棋盘：15列×30行<br/>
              ⏰ 模式：无限时间，死亡结束<br/>
              🎯 目标：消除更多行数获得高分
            </view>
          </view>
          <view class="play-btn">
            <text style="font-size: 60rpx;">▶️</text>
          </view>
        </view>
        <view class="game-stats mt-20">
          <text class="score">最高分: {{tetrisBest}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="actions-section" wx:if="{{userData.username}}">
    <view class="game-card">
      <view class="p-20">
        <view class="game-title" style="text-align: center; margin-bottom: 20rpx;">🎯 游戏中心</view>
        <view class="flex" style="gap: 15rpx;">
          <button class="btn btn-success" style="flex: 1;" bindtap="viewLeaderboard">
            🏆 排行榜
          </button>
          <button class="btn btn-secondary" style="flex: 1;" bindtap="clearData">
            🗑️ 清除数据
          </button>
        </view>
      </view>
    </view>
  </view>
</view>
