-- 芳芳消消乐 - 简化版MySQL数据库配置
-- 使用方法: mysql -u用户名 -p < simple_schema.sql

-- 创建数据库
CREATE DATABASE IF NOT EXISTS fangfang_game CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE fangfang_game;

-- 创建排行榜表
CREATE TABLE IF NOT EXISTS leaderboard (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    player_name VARCHAR(50) NOT NULL COMMENT '玩家姓名',
    score INT NOT NULL DEFAULT 0 COMMENT '游戏分数',
    time_used INT NOT NULL DEFAULT 0 COMMENT '实际游戏用时（秒）',
    time_limit INT NOT NULL DEFAULT 180 COMMENT '游戏时长限制（秒）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建索引
CREATE INDEX idx_ranking ON leaderboard(score DESC, time_used ASC);
CREATE INDEX idx_player ON leaderboard(player_name);
CREATE INDEX idx_date ON leaderboard(created_at DESC);

-- 插入测试数据
INSERT INTO leaderboard (player_name, score, time_used, time_limit) VALUES
('芳芳', 2500, 120, 180),
('小明', 2200, 150, 180),
('小红', 2000, 100, 180);
