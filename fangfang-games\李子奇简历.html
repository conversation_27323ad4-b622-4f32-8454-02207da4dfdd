<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>李子奇 - 个人简历</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        
        h2 {
            color: #2980b9;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
            margin-bottom: 15px;
        }
        
        h3 {
            color: #34495e;
            margin-bottom: 10px;
        }
        
        .contact-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .contact-info ul {
            list-style: none;
            padding: 0;
            margin: 0;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 10px;
        }
        
        .contact-info li {
            padding: 5px 0;
        }
        
        .section {
            margin-bottom: 25px;
        }
        
        .job-item, .project-item {
            margin-bottom: 20px;
            padding: 15px;
            border-left: 3px solid #e74c3c;
            background-color: #fafafa;
        }
        
        .job-title {
            font-weight: bold;
            color: #2c3e50;
            font-size: 1.1em;
        }
        
        .job-time {
            color: #7f8c8d;
            font-style: italic;
            margin-bottom: 10px;
        }
        
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .skill-category {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
        }
        
        .skill-category h4 {
            color: #2980b9;
            margin-top: 0;
            margin-bottom: 10px;
        }
        
        ul {
            padding-left: 20px;
        }
        
        li {
            margin-bottom: 5px;
        }
        
        .highlight {
            background-color: #fff3cd;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #7f8c8d;
            font-size: 0.9em;
        }
        
        @media print {
            body {
                font-size: 12px;
                line-height: 1.4;
            }
            .job-item, .project-item {
                break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <h1>李子奇 - 个人简历</h1>
    
    <div class="contact-info">
        <ul>
            <li><strong>姓名：</strong>李子奇</li>
            <li><strong>性别：</strong>男</li>
            <li><strong>年龄：</strong>XX岁</li>
            <li><strong>联系电话：</strong>+86 XXX-XXXX-XXXX</li>
            <li><strong>邮箱：</strong><EMAIL></li>
            <li><strong>现居地址：</strong>北京市/上海市/深圳市</li>
            <li><strong>求职意向：</strong>软件开发工程师/前端工程师</li>
        </ul>
    </div>

    <div class="section">
        <h2>🎓 教育背景</h2>
        <div class="job-item">
            <div class="job-title">XX大学 | 计算机科学与技术专业 | 本科/硕士</div>
            <div class="job-time">20XX年9月 - 20XX年6月</div>
            <p><strong>主要课程：</strong>数据结构、算法设计、数据库系统、软件工程、计算机网络</p>
            <p><strong>GPA：</strong>X.X/4.0 (专业排名前X%)</p>
            <p><strong>获奖情况：</strong></p>
            <ul>
                <li>优秀学生奖学金（连续X年）</li>
                <li>XX编程竞赛X等奖</li>
                <li>优秀毕业设计</li>
            </ul>
        </div>
    </div>

    <div class="section">
        <h2>💼 工作经历</h2>
        
        <div class="job-item">
            <div class="job-title">XX科技有限公司 | 软件开发工程师</div>
            <div class="job-time">20XX年X月 - 至今</div>
            <p><strong>主要职责：</strong></p>
            <ul>
                <li>负责公司核心产品的前端开发，使用React/Vue.js技术栈</li>
                <li>参与系统架构设计，优化代码性能，提升用户体验</li>
                <li>与产品经理、UI设计师协作，完成产品需求开发</li>
                <li>指导初级开发人员，参与代码审查和技术分享</li>
            </ul>
            <p><strong>主要成就：</strong></p>
            <ul>
                <li>主导开发的XX项目，用户量增长<span class="highlight">XX%</span>，获得公司年度最佳项目奖</li>
                <li>优化系统性能，页面加载速度提升<span class="highlight">XX%</span></li>
                <li>建立前端开发规范，提高团队开发效率<span class="highlight">XX%</span></li>
            </ul>
        </div>

        <div class="job-item">
            <div class="job-title">XX互联网公司 | 前端开发实习生</div>
            <div class="job-time">20XX年X月 - 20XX年X月</div>
            <p><strong>主要职责：</strong></p>
            <ul>
                <li>参与公司官网和管理后台的开发维护</li>
                <li>学习并应用现代前端开发技术和工具</li>
                <li>协助完成移动端H5页面开发</li>
            </ul>
        </div>
    </div>

    <div class="section">
        <h2>🛠️ 技能特长</h2>
        <div class="skills-grid">
            <div class="skill-category">
                <h4>编程语言</h4>
                <ul>
                    <li><strong>JavaScript/TypeScript：</strong>熟练掌握ES6+语法，理解异步编程</li>
                    <li><strong>HTML5/CSS3：</strong>精通语义化标签，熟悉响应式设计</li>
                    <li><strong>Python：</strong>熟悉Django/Flask框架，能够进行后端开发</li>
                    <li><strong>Java：</strong>了解Spring Boot框架，具备基础开发能力</li>
                </ul>
            </div>
            
            <div class="skill-category">
                <h4>前端技术</h4>
                <ul>
                    <li><strong>框架：</strong>React、Vue.js、Angular</li>
                    <li><strong>状态管理：</strong>Redux、Vuex、MobX</li>
                    <li><strong>构建工具：</strong>Webpack、Vite、Rollup</li>
                    <li><strong>CSS预处理器：</strong>Sass、Less、Stylus</li>
                    <li><strong>UI框架：</strong>Ant Design、Element UI、Material-UI</li>
                </ul>
            </div>
            
            <div class="skill-category">
                <h4>后端技术</h4>
                <ul>
                    <li><strong>Node.js：</strong>Express、Koa框架开发经验</li>
                    <li><strong>数据库：</strong>MySQL、MongoDB、Redis</li>
                    <li><strong>服务器：</strong>Linux基础操作，Nginx配置</li>
                    <li><strong>云服务：</strong>阿里云、腾讯云部署经验</li>
                </ul>
            </div>
            
            <div class="skill-category">
                <h4>开发工具</h4>
                <ul>
                    <li><strong>版本控制：</strong>Git、SVN</li>
                    <li><strong>开发环境：</strong>VS Code、WebStorm</li>
                    <li><strong>项目管理：</strong>Jira、Trello、禅道</li>
                    <li><strong>设计工具：</strong>Figma、Sketch基础使用</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>🏆 项目经验</h2>
        
        <div class="project-item">
            <div class="job-title">芳芳消消乐游戏平台</div>
            <div class="job-time">20XX年X月 - 20XX年X月</div>
            <p><strong>项目描述：</strong>基于Web技术开发的多人在线休闲游戏平台，包含消消乐、连连看、俄罗斯方块等多种游戏模式。</p>
            <p><strong>技术栈：</strong></p>
            <ul>
                <li>前端：HTML5 Canvas、JavaScript ES6+、CSS3动画</li>
                <li>后端：Node.js + Express + MySQL</li>
                <li>部署：Docker + Nginx</li>
            </ul>
            <p><strong>主要职责：</strong></p>
            <ul>
                <li>负责游戏核心逻辑开发，实现消除算法和动画效果</li>
                <li>设计并实现排行榜系统，支持多种游戏模式数据统计</li>
                <li>优化游戏性能，确保流畅的用户体验</li>
                <li>实现响应式设计，支持PC和移动端访问</li>
            </ul>
            <p><strong>项目成果：</strong></p>
            <ul>
                <li>游戏平台日活跃用户达到<span class="highlight">XXX人</span></li>
                <li>游戏加载速度优化至<span class="highlight">X秒内</span></li>
                <li>获得用户好评率<span class="highlight">XX%以上</span></li>
            </ul>
        </div>

        <div class="project-item">
            <div class="job-title">XX管理系统</div>
            <div class="job-time">20XX年X月 - 20XX年X月</div>
            <p><strong>项目描述：</strong>企业内部管理系统，包含用户管理、权限控制、数据统计等功能。</p>
            <p><strong>技术栈：</strong>React + Ant Design + Node.js + MySQL</p>
            <p><strong>主要职责：</strong></p>
            <ul>
                <li>负责前端页面开发和交互逻辑实现</li>
                <li>参与数据库设计和API接口开发</li>
                <li>实现用户权限管理和数据安全控制</li>
            </ul>
        </div>
    </div>

    <div class="section">
        <h2>🌟 个人特点</h2>
        <ul>
            <li><strong>学习能力强：</strong>能够快速掌握新技术，持续关注行业发展趋势</li>
            <li><strong>团队协作：</strong>具备良好的沟通能力，能够与不同角色的同事高效协作</li>
            <li><strong>问题解决：</strong>具备较强的逻辑思维和问题分析能力</li>
            <li><strong>责任心强：</strong>对工作认真负责，能够按时保质完成任务</li>
            <li><strong>创新思维：</strong>喜欢尝试新技术，能够提出创新性的解决方案</li>
        </ul>
    </div>

    <div class="section">
        <h2>🎯 职业规划</h2>
        <ul>
            <li><strong>短期目标：</strong>在前端/全栈开发领域深入发展，提升技术深度和广度</li>
            <li><strong>中期目标：</strong>成为技术专家，能够独立负责大型项目的技术架构</li>
            <li><strong>长期目标：</strong>向技术管理方向发展，带领团队完成更有挑战性的项目</li>
        </ul>
    </div>

    <div class="section">
        <h2>📞 联系方式</h2>
        <div class="contact-info">
            <ul>
                <li><strong>手机：</strong>+86 XXX-XXXX-XXXX</li>
                <li><strong>邮箱：</strong><EMAIL></li>
                <li><strong>GitHub：</strong>https://github.com/liziqi</li>
                <li><strong>个人博客：</strong>https://liziqi.blog.com</li>
                <li><strong>LinkedIn：</strong>https://linkedin.com/in/liziqi</li>
            </ul>
        </div>
    </div>

    <div class="footer">
        <p>简历最后更新时间：20XX年X月</p>
    </div>
</body>
</html>
