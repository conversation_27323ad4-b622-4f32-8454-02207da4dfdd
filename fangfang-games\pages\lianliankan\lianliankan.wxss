/* 连连看游戏样式 */

.welcome-screen {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80vh;
}

.time-options {
  margin: 20rpx 0;
}

.game-rules {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10rpx;
  padding: 20rpx;
}

.game-screen {
  padding: 20rpx 0;
}

.game-status {
  display: flex;
  justify-content: space-around;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.status-item {
  text-align: center;
}

.status-label {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 5rpx;
}

.status-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.status-value.warning {
  color: #ff6b6b;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.game-board {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.board-row {
  display: flex;
  justify-content: center;
  margin-bottom: 8rpx;
}

.board-row:last-child {
  margin-bottom: 0;
}

.board-cell {
  width: 60rpx;
  height: 60rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  margin: 2rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  transition: all 0.2s ease;
}

.board-cell.selected {
  border-color: #ff6b6b;
  background: #ffe6e6;
  transform: scale(1.1);
  box-shadow: 0 4rpx 15rpx rgba(255, 107, 107, 0.3);
}

.board-cell.matched {
  background: #f0f0f0;
  border-color: #ccc;
  opacity: 0.3;
}

.cell-content {
  font-size: 32rpx;
}

.game-controls {
  display: flex;
  justify-content: space-around;
  gap: 15rpx;
}

.control-btn {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  border: 2rpx solid #ddd;
  border-radius: 30rpx;
  padding: 15rpx 10rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.control-btn:disabled {
  opacity: 0.5;
  background: #f5f5f5;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 20rpx;
  width: 80%;
  max-width: 500rpx;
  overflow: hidden;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
}

.modal-header {
  background: linear-gradient(135deg, #ff6b6b, #ff8e53);
  color: white;
  padding: 30rpx;
  text-align: center;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
}

.modal-body {
  padding: 30rpx;
  text-align: center;
}

.result-stats {
  margin: 20rpx 0;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
  font-size: 28rpx;
}

.stat-row:last-child {
  margin-bottom: 0;
}

.modal-footer {
  display: flex;
  border-top: 2rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  padding: 30rpx;
  border: none;
  background: white;
  font-size: 28rpx;
  font-weight: 500;
}

.modal-btn.secondary {
  color: #666;
  border-right: 2rpx solid #f0f0f0;
}

.modal-btn.primary {
  color: #ff6b6b;
  font-weight: bold;
}
