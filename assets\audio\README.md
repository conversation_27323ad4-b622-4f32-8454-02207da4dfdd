# 游戏音频文件说明

## 📁 音频文件目录结构

```
assets/audio/
├── background.mp3          # 背景音乐
├── match.mp3              # 匹配成功音效
├── swap.mp3               # 交换音效
├── click.mp3              # 点击音效
├── hint.mp3               # 提示音效
├── game-start.mp3         # 游戏开始音效
├── game-win.mp3           # 游戏胜利音效
├── game-over.mp3          # 游戏结束音效
├── button-hover.mp3       # 按钮悬停音效
├── button-click.mp3       # 按钮点击音效
├── popup.mp3              # 弹窗出现音效
├── connect.mp3            # 连线成功音效（连连看）
├── eliminate.mp3          # 消除音效（连连看）
├── combo.mp3              # 连击音效（消消乐）
└── cascade.mp3            # 连锁反应音效（消消乐）
```

## 🎵 音频文件要求

### 格式要求
- **格式**: MP3 (推荐) 或 OGG
- **采样率**: 44.1kHz 或 48kHz
- **比特率**: 128kbps - 320kbps
- **声道**: 立体声或单声道

### 时长建议
- **背景音乐**: 2-5分钟（循环播放）
- **音效**: 0.1-2秒
- **游戏状态音效**: 1-3秒

### 音量建议
- **背景音乐**: 相对较低，不干扰游戏
- **音效**: 清晰可听，但不刺耳
- **建议进行音量标准化处理**

## 🎮 音效使用场景

### 背景音乐
- **background.mp3**: 游戏全程循环播放的背景音乐

### 游戏操作音效
- **click.mp3**: 卡片点击
- **swap.mp3**: 卡片交换（消消乐）
- **match.mp3**: 匹配成功（通用）
- **hint.mp3**: 使用提示功能

### 连连看专用音效
- **connect.mp3**: 两张卡片成功连接
- **eliminate.mp3**: 卡片消除动画

### 消消乐专用音效
- **combo.mp3**: 连击效果
- **cascade.mp3**: 连锁消除效果

### 游戏状态音效
- **game-start.mp3**: 游戏开始时播放
- **game-win.mp3**: 游戏胜利时播放
- **game-over.mp3**: 游戏失败/时间到时播放

### UI交互音效
- **button-hover.mp3**: 鼠标悬停在按钮上
- **button-click.mp3**: 点击按钮
- **popup.mp3**: 弹窗出现

## 🔧 技术实现

### 音频管理器特性
- ✅ 音量控制（背景音乐和音效分别控制）
- ✅ 静音功能
- ✅ 设置持久化（localStorage）
- ✅ 错误处理（音频加载失败时的降级处理）
- ✅ 预加载机制
- ✅ 浏览器兼容性处理

### 浏览器兼容性
- 支持现代浏览器的自动播放策略
- 处理用户交互前的音频播放限制
- 提供音频控制UI

## 📝 使用说明

### 添加新音效
1. 将音频文件放入 `assets/audio/` 目录
2. 在 `AudioManager.js` 中添加音频文件引用
3. 在相应的游戏逻辑中调用 `audioManager.playSound('soundName')`

### 音效调用示例
```javascript
// 播放背景音乐
window.audioManager.playBackgroundMusic();

// 播放音效
window.audioManager.playSound('match');

// 设置音量
window.audioManager.setMusicVolume(0.3);
window.audioManager.setEffectVolume(0.5);

// 静音控制
window.audioManager.setMuted(true);
```

## 🎨 音频资源建议

### 免费音频资源网站
- **Freesound.org**: 免费音效库
- **Zapsplat**: 高质量音效（需注册）
- **Adobe Audition**: 内置音效库
- **YouTube Audio Library**: 免费背景音乐

### 音效风格建议
- **背景音乐**: 轻松愉快，不过于激烈
- **成功音效**: 明亮、积极的音调
- **失败音效**: 低沉但不沮丧
- **UI音效**: 简洁、现代感

## ⚠️ 注意事项

1. **版权问题**: 确保使用的音频文件有合法使用权
2. **文件大小**: 控制音频文件大小，避免影响加载速度
3. **浏览器限制**: 现代浏览器需要用户交互后才能播放音频
4. **音量平衡**: 确保各音效之间音量协调
5. **格式兼容**: 建议提供多种格式以确保兼容性
