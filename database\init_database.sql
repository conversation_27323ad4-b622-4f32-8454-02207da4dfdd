-- 创建数据库
CREATE DATABASE IF NOT EXISTS fangfang_game CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE fangfang_game;

-- 创建排行榜表
CREATE TABLE IF NOT EXISTS leaderboard (
    id INT AUTO_INCREMENT PRIMARY KEY,
    player_name VARCHAR(50) NOT NULL COMMENT '玩家姓名',
    score INT NOT NULL DEFAULT 0 COMMENT '游戏分数',
    time_used INT NOT NULL DEFAULT 0 COMMENT '用时（秒）',
    time_limit INT NOT NULL DEFAULT 180 COMMENT '时间限制（秒）',
    game_type VARCHAR(20) NOT NULL DEFAULT 'match3' COMMENT '游戏类型：match3或lianliankan',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_score_time (score DESC, time_used ASC),
    INDEX idx_game_type (game_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='游戏排行榜';

-- 显示创建结果
SELECT '数据库初始化完成' as message;
SELECT COUNT(*) as total_records FROM leaderboard;
