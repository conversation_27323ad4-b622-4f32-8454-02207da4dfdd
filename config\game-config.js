// 游戏配置文件
const GAME_CONFIG = {
    // 图片资源配置
    IMAGES: {
        BASE_PATH: 'assets/images/',
        FILES: [
            'fangfang-01.jpg',
            'fangfang-02.jpg', 
            'fangfang-03.jpg',
            'fangfang-04.jpg',
            'fangfang-05.jpg',
            'fangfang-06.jpg'
        ],
        NAMES: [
            '照片1', '照片2', '照片3', '照片4', '照片5', '照片6'
        ]
    },

    // 消消乐游戏配置
    MATCH_THREE: {
        GRID_SIZE: 8,
        CELL_SIZE: 60,
        BORDER_SIZE: 2,
        BORDER_RADIUS: 8,
        COLORS: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#8e44ad', '#feca57', '#ff9ff3'],
        TIME_OPTIONS: [
            { label: '2分钟', value: 120 },
            { label: '3分钟', value: 180 },
            { label: '4分钟', value: 240 },
            { label: '5分钟', value: 300 }
        ],
        ANIMATION: {
            DURATION: 200,
            SWAP_DURATION: 300
        },
        HINT: {
            DELAY: 20000, // 20秒后显示提示
            HIGHLIGHT_DURATION: 2000
        }
    },

    // 连连看游戏配置
    LIANLIANKAN: {
        ROWS: 6,
        COLS: 8,
        TIME_OPTIONS: [
            { label: '30秒', value: 30 },
            { label: '1分钟', value: 60 }
        ],
        HINT_COUNT: 3,
        SCORE_PER_MATCH: 100
    },

    // API配置
    API: {
        BASE_URL: '/api',
        ENDPOINTS: {
            LEADERBOARD: '/leaderboard',
            HEALTH: '/health'
        }
    },

    // 排行榜配置
    LEADERBOARD: {
        MAX_ENTRIES: 10,
        GAME_TYPES: {
            MATCH3: 'match3',
            LIANLIANKAN: 'lianliankan'
        }
    },

    // UI配置
    UI: {
        COLORS: {
            PRIMARY: '#667eea',
            SECONDARY: '#764ba2',
            SUCCESS: '#4ecdc4',
            WARNING: '#feca57',
            DANGER: '#ff6b6b'
        },
        ANIMATIONS: {
            FADE_DURATION: 300,
            SLIDE_DURATION: 400
        }
    }
};

// 如果在Node.js环境中，导出配置
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GAME_CONFIG;
}

// 如果在浏览器环境中，设置为全局变量
if (typeof window !== 'undefined') {
    window.GAME_CONFIG = GAME_CONFIG;
}
