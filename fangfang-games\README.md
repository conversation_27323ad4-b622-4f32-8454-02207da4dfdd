# 🎮 芳芳的游戏世界

一个集成多种经典小游戏的微信小程序平台，为用户提供丰富的游戏体验。

## ✨ 游戏特色

### 🎯 核心功能
- **用户系统**: 设置游戏昵称，记录游戏数据
- **多游戏平台**: 集成连连看、消消乐、俄罗斯方块
- **排行榜系统**: 记录最高分数，展示游戏排名
- **成就系统**: 解锁游戏成就，增加游戏乐趣

### 🔗 连连看游戏
- **时间模式**: 30秒/60秒两种时间选择
- **提示系统**: 30秒游戏2次提示，60秒游戏4次提示
- **智能连接**: 支持直线和一次转弯连接
- **精美界面**: 使用emoji图案，视觉效果佳

## 🎮 游戏玩法

### 连连看规则
1. **目标**: 在限定时间内消除所有相同图案
2. **操作**: 点击两个相同的图案进行连接
3. **连接**: 连接路径不能超过2个转弯
4. **提示**: 可使用提示功能寻找可消除的配对
5. **计分**: 每消除一对获得100分，剩余时间有奖励

### 游戏控制
- **⏸️ 暂停**: 暂停游戏计时
- **💡 提示**: 高亮显示可消除的配对
- **🔄 重新开始**: 重置当前游戏

## 📊 数据系统

### 用户数据
```javascript
{
  username: '玩家昵称',
  totalScore: 总分数,
  gamesPlayed: 游戏次数,
  achievements: ['成就列表']
}
```

### 游戏记录
```javascript
{
  game: '游戏名称',
  score: 得分,
  time: '游戏时间',
  username: '玩家昵称'
}
```

### 排行榜功能
- **分类排行**: 按游戏类型查看排行榜
- **全局排行**: 查看所有游戏的综合排名
- **实时更新**: 游戏结束后立即更新排行

## 🏆 成就系统

### 成就类型
- **🎮 初次体验**: 完成第一次游戏
- **🏆 游戏达人**: 游戏次数达到10次
- **⭐ 高分玩家**: 总分数达到1000分

### 成就奖励
- 获得成就时显示祝贺提示
- 在个人资料中展示成就徽章
- 增加游戏成就感和持续性

## 🎨 界面设计

### 设计风格
- **渐变背景**: 紫色渐变营造梦幻氛围
- **卡片布局**: 白色半透明卡片，层次分明
- **圆角设计**: 所有元素采用圆角，温和友好
- **动画效果**: 按钮点击、方块选择有动画反馈

### 色彩方案
- **主色调**: #ff6b6b (温暖红色)
- **辅助色**: #4ecdc4 (清新绿色)
- **背景色**: #667eea → #764ba2 (紫色渐变)
- **文字色**: #333 (深灰色)

## 🛠️ 技术实现

### 核心技术
- **微信小程序**: 原生小程序开发
- **本地存储**: wx.setStorageSync/getStorageSync
- **定时器**: setInterval 实现游戏计时
- **数组算法**: 游戏逻辑和路径查找

### 连连看算法
```javascript
// 连接检查算法
canConnect(pos1, pos2) {
  // 直线连接检查
  if (r1 === r2 || c1 === c2) {
    return this.isPathClear(pos1, pos2)
  }
  
  // 一次转弯连接检查
  const corner1 = { row: r1, col: c2 }
  const corner2 = { row: r2, col: c1 }
  
  return this.checkCornerPath(pos1, pos2, corner1) ||
         this.checkCornerPath(pos1, pos2, corner2)
}
```

### 数据管理
- **全局状态**: App.globalData 管理用户数据
- **本地持久化**: 游戏记录和用户数据本地存储
- **实时同步**: 页面间数据实时同步更新

## 📱 使用指南

### 启动游戏
1. **导入项目**: 选择 `fangfang-games` 文件夹
2. **配置信息**: AppID设为 `touristappid`
3. **编译运行**: 点击编译按钮
4. **设置用户名**: 首次使用需设置游戏昵称

### 游戏流程
```
设置用户名 → 选择游戏 → 开始游戏 → 查看排行榜
    ↓           ↓         ↓         ↓
  👤用户     🎮游戏选择   🔗连连看   🏆排行榜
```

### 连连看游戏
1. **选择时间**: 30秒或60秒
2. **开始游戏**: 点击开始按钮
3. **连接图案**: 点击相同图案进行连接
4. **使用提示**: 遇到困难可使用提示
5. **完成游戏**: 消除所有图案或时间结束

## 🔮 未来规划

### 即将推出
- **💎 消消乐**: 三消类益智游戏
- **🧩 俄罗斯方块**: 经典方块游戏
- **🎵 背景音乐**: 游戏背景音效
- **🎨 主题切换**: 多种游戏主题

### 功能扩展
- **联机对战**: 多人游戏模式
- **每日挑战**: 每日特殊关卡
- **道具系统**: 游戏辅助道具
- **社交分享**: 分享游戏成绩

## 📋 开发说明

### 项目结构
```
fangfang-games/
├── app.js                 # 应用入口
├── app.json              # 应用配置
├── app.wxss              # 全局样式
├── pages/
│   ├── index/            # 游戏世界主页
│   ├── lianliankan/      # 连连看游戏
│   ├── xiaoxiaole/       # 消消乐游戏
│   ├── tetris/           # 俄罗斯方块
│   └── leaderboard/      # 排行榜页面
└── project.config.json   # 项目配置
```

### 核心API
```javascript
// 用户管理
app.setUsername(username)        // 设置用户名
app.updateScore(game, score)     // 更新分数
app.checkAchievements()          // 检查成就

// 数据获取
app.getLeaderboard(game)         // 获取排行榜
app.globalData.userData          // 用户数据
```

### 扩展开发
- 添加新游戏只需创建对应页面
- 使用统一的分数系统和排行榜
- 遵循现有的UI设计规范

## 🎉 特色亮点

### 用户体验
- **一键开始**: 设置用户名后即可开始游戏
- **数据持久**: 游戏数据本地保存，不会丢失
- **成就激励**: 解锁成就增加游戏动力
- **排行竞争**: 排行榜激发竞争意识

### 技术亮点
- **算法优化**: 高效的连接检查算法
- **性能优化**: 合理的数据结构和状态管理
- **兼容性好**: 使用稳定的基础库版本
- **代码规范**: 清晰的代码结构和注释

芳芳的游戏世界，让每一次游戏都充满乐趣！🎮✨
