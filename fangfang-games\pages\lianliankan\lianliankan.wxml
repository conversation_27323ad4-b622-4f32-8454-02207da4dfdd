<!-- 连连看游戏页面 -->
<view class="container">
  <!-- 游戏未开始 -->
  <view wx:if="{{!gameStarted}}" class="welcome-screen">
    <view class="game-card">
      <view class="p-30" style="text-align: center;">
        <view class="game-icon">🔗</view>
        <view class="game-title">芳芳连连看</view>
        <view class="game-desc mb-20">连接相同的图案，消除所有方块</view>
        
        <view class="time-options">
          <view class="subtitle" style="margin-bottom: 15rpx;">选择游戏时间</view>
          <view class="flex" style="gap: 15rpx; justify-content: center;">
            <button 
              class="btn {{selectedTime === 30 ? 'btn-primary' : 'btn-secondary'}}" 
              bindtap="selectTime" 
              data-time="30"
            >
              30秒
            </button>
            <button 
              class="btn {{selectedTime === 60 ? 'btn-primary' : 'btn-secondary'}}" 
              bindtap="selectTime" 
              data-time="60"
            >
              60秒
            </button>
          </view>
        </view>

        <view class="game-rules mt-20">
          <view style="font-size: 24rpx; color: #666; line-height: 1.5;">
            🎯 游戏规则：<br/>
            • 点击两个相同的图案进行连接<br/>
            • 连接路径不能超过2个转弯<br/>
            • 30秒游戏有2次提示，60秒游戏有4次提示<br/>
            • 在时间内消除所有图案即可获胜
          </view>
        </view>

        <button class="btn btn-primary mt-30" style="width: 200rpx;" bindtap="startGame">
          开始游戏
        </button>
      </view>
    </view>
  </view>

  <!-- 游戏进行中 -->
  <view wx:if="{{gameStarted}}" class="game-screen">
    <!-- 游戏状态栏 -->
    <view class="game-status">
      <view class="status-item">
        <text class="status-label">⏰</text>
        <text class="status-value {{timeLeft <= 10 ? 'warning' : ''}}">{{timeLeft}}s</text>
      </view>
      <view class="status-item">
        <text class="status-label">🎯</text>
        <text class="status-value">{{score}}</text>
      </view>
      <view class="status-item">
        <text class="status-label">💡</text>
        <text class="status-value">{{hintsLeft}}</text>
      </view>
    </view>

    <!-- 游戏棋盘 -->
    <view class="game-board">
      <view class="board-row" wx:for="{{gameBoard}}" wx:key="rowIndex" wx:for-index="rowIndex">
        <view 
          class="board-cell {{item.selected ? 'selected' : ''}} {{item.matched ? 'matched' : ''}}"
          wx:for="{{item}}" 
          wx:key="colIndex" 
          wx:for-index="colIndex"
          wx:for-item="cell"
          bindtap="selectCell"
          data-row="{{rowIndex}}"
          data-col="{{colIndex}}"
        >
          <text wx:if="{{!cell.matched}}" class="cell-content">{{cell.emoji}}</text>
        </view>
      </view>
    </view>

    <!-- 游戏控制 -->
    <view class="game-controls">
      <button class="control-btn" bindtap="useHint" disabled="{{hintsLeft <= 0}}">
        💡 提示 ({{hintsLeft}})
      </button>
      <button class="control-btn" bindtap="pauseGame">
        ⏸️ 暂停
      </button>
      <button class="control-btn" bindtap="restartGame">
        🔄 重新开始
      </button>
    </view>
  </view>

  <!-- 游戏结束弹窗 -->
  <view class="modal-overlay" wx:if="{{showGameOver}}">
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">{{gameWon ? '🎉 恭喜过关！' : '⏰ 时间到！'}}</text>
      </view>
      <view class="modal-body">
        <view class="result-stats">
          <view class="stat-row">
            <text>最终得分：</text>
            <text class="score">{{score}}</text>
          </view>
          <view class="stat-row">
            <text>游戏时间：</text>
            <text>{{selectedTime}}秒</text>
          </view>
          <view class="stat-row">
            <text>消除数量：</text>
            <text>{{eliminatedPairs}}</text>
          </view>
        </view>
      </view>
      <view class="modal-footer">
        <button class="modal-btn secondary" bindtap="backToHome">返回首页</button>
        <button class="modal-btn primary" bindtap="playAgain">再玩一次</button>
      </view>
    </view>
  </view>

  <!-- 暂停弹窗 -->
  <view class="modal-overlay" wx:if="{{showPause}}">
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">⏸️ 游戏暂停</text>
      </view>
      <view class="modal-body">
        <text>游戏已暂停，点击继续按钮恢复游戏</text>
      </view>
      <view class="modal-footer">
        <button class="modal-btn secondary" bindtap="backToHome">退出游戏</button>
        <button class="modal-btn primary" bindtap="resumeGame">继续游戏</button>
      </view>
    </view>
  </view>
</view>
